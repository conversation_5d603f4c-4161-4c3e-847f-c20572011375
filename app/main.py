"""
Main FastAPI application entry point.

This module creates and configures the FastAPI application with all
necessary middleware, routers, and startup/shutdown event handlers.
"""

from contextlib import asynccontextmanager
from typing import AsyncGenerator

from fastapi import FastAP<PERSON>
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.responses import JSONResponse
import structlog

from app.core.config import settings
from app.core.database import close_db, init_db
from app.api.v1.api import api_router

# Configure structured logging
structlog.configure(
    processors=[
        structlog.stdlib.filter_by_level,
        structlog.stdlib.add_logger_name,
        structlog.stdlib.add_log_level,
        structlog.stdlib.PositionalArgumentsFormatter(),
        structlog.processors.TimeStamper(fmt="iso"),
        structlog.processors.StackInfoRenderer(),
        structlog.processors.format_exc_info,
        structlog.processors.UnicodeDecoder(),
        structlog.processors.JSONRenderer()
    ],
    context_class=dict,
    logger_factory=structlog.stdlib.LoggerFactory(),
    wrapper_class=structlog.stdlib.BoundLogger,
    cache_logger_on_first_use=True,
)

logger = structlog.get_logger(__name__)


@asynccontextmanager
async def lifespan(app: FastAPI) -> AsyncGenerator[None, None]:
    """
    Application lifespan manager for startup and shutdown events.
    
    This function handles initialization and cleanup tasks that need
    to run when the application starts and stops.
    
    Args:
        app: The FastAPI application instance
        
    Yields:
        None: Control back to FastAPI during application runtime
    """
    # Startup
    logger.info("Starting up Supplement Tracker API")
    
    if not settings.TESTING:
        await init_db()
        logger.info("Database initialized")
    
    logger.info("Application startup complete")
    
    yield
    
    # Shutdown
    logger.info("Shutting down Supplement Tracker API")
    await close_db()
    logger.info("Database connections closed")
    logger.info("Application shutdown complete")


def create_application() -> FastAPI:
    """
    Create and configure the FastAPI application.
    
    This function sets up the FastAPI app with all necessary configuration,
    middleware, and route handlers.
    
    Returns:
        FastAPI: Configured application instance
    """
    app = FastAPI(
        title=settings.PROJECT_NAME,
        description="Community-Driven Supplement Research Platform",
        version="0.1.0",
        openapi_url=f"{settings.API_V1_STR}/openapi.json",
        docs_url=f"{settings.API_V1_STR}/docs",
        redoc_url=f"{settings.API_V1_STR}/redoc",
        lifespan=lifespan,
    )
    
    # Set up CORS middleware
    if settings.BACKEND_CORS_ORIGINS:
        app.add_middleware(
            CORSMiddleware,
            allow_origins=[str(origin) for origin in settings.BACKEND_CORS_ORIGINS],
            allow_credentials=True,
            allow_methods=["*"],
            allow_headers=["*"],
        )
    
    # Add trusted host middleware for security
    app.add_middleware(
        TrustedHostMiddleware,
        allowed_hosts=["localhost", "127.0.0.1", settings.SERVER_NAME]
    )
    
    # Include API router
    app.include_router(api_router, prefix=settings.API_V1_STR)
    
    # Health check endpoint
    @app.get("/health")
    async def health_check():
        """
        Health check endpoint for monitoring and load balancers.
        
        Returns:
            dict: Health status information
        """
        return JSONResponse(
            content={
                "status": "healthy",
                "service": "supplement-tracker-api",
                "version": "0.1.0"
            }
        )
    
    # Root endpoint
    @app.get("/")
    async def root():
        """
        Root endpoint with basic API information.
        
        Returns:
            dict: API information and links
        """
        return JSONResponse(
            content={
                "message": "Welcome to the Supplement Tracker API",
                "version": "0.1.0",
                "docs_url": f"{settings.API_V1_STR}/docs",
                "health_url": "/health"
            }
        )
    
    return app


# Create the application instance
app = create_application()


if __name__ == "__main__":
    import uvicorn
    
    uvicorn.run(
        "app.main:app",
        host="0.0.0.0",
        port=8000,
        reload=settings.DEBUG,
        log_level="info" if not settings.DEBUG else "debug",
    )
