"""
Tests for security utilities.

This module contains tests for password hashing, JWT token handling,
and other security-related functionality.
"""

import pytest
from datetime import datetime, timedelta

from app.core.security import (
    create_access_token,
    decode_access_token,
    get_password_hash,
    verify_password,
    generate_password_reset_token,
    verify_password_reset_token,
)


@pytest.mark.unit
def test_password_hashing():
    """
    Test password hashing and verification.
    
    This test verifies that passwords are properly hashed
    and can be verified correctly.
    """
    password = "TestPassword123"
    
    # Hash the password
    hashed = get_password_hash(password)
    
    # Verify the hash is different from the original password
    assert hashed != password
    assert len(hashed) > 0
    
    # Verify the password matches the hash
    assert verify_password(password, hashed) is True
    
    # Verify wrong password doesn't match
    assert verify_password("WrongPassword", hashed) is False


@pytest.mark.unit
def test_access_token_creation_and_verification():
    """
    Test JWT access token creation and verification.
    
    This test verifies that JWT tokens are properly created
    and can be decoded to retrieve the original subject.
    """
    subject = "test-user-123"
    
    # Create token
    token = create_access_token(subject=subject)
    
    assert isinstance(token, str)
    assert len(token) > 0
    
    # Decode token
    payload = decode_access_token(token)
    
    assert payload is not None
    assert payload["sub"] == subject
    assert "exp" in payload


@pytest.mark.unit
def test_access_token_with_custom_expiration():
    """
    Test JWT access token with custom expiration time.
    
    This test verifies that custom expiration times
    are properly set in JWT tokens.
    """
    subject = "test-user-123"
    expires_delta = timedelta(minutes=30)
    
    # Create token with custom expiration
    token = create_access_token(subject=subject, expires_delta=expires_delta)
    
    # Decode token
    payload = decode_access_token(token)
    
    assert payload is not None
    assert payload["sub"] == subject
    
    # Check expiration is approximately correct (within 1 minute tolerance)
    exp_timestamp = payload["exp"]
    expected_exp = datetime.utcnow() + expires_delta
    actual_exp = datetime.fromtimestamp(exp_timestamp)
    
    time_diff = abs((expected_exp - actual_exp).total_seconds())
    assert time_diff < 60  # Within 1 minute


@pytest.mark.unit
def test_invalid_token_decoding():
    """
    Test decoding of invalid JWT tokens.
    
    This test verifies that invalid tokens return None
    when decoded.
    """
    # Test with invalid token
    invalid_token = "invalid.token.here"
    payload = decode_access_token(invalid_token)
    
    assert payload is None
    
    # Test with empty token
    payload = decode_access_token("")
    assert payload is None


@pytest.mark.unit
def test_password_reset_token():
    """
    Test password reset token generation and verification.
    
    This test verifies that password reset tokens work
    correctly for the reset flow.
    """
    email = "<EMAIL>"
    
    # Generate reset token
    token = generate_password_reset_token(email)
    
    assert isinstance(token, str)
    assert len(token) > 0
    
    # Verify reset token
    verified_email = verify_password_reset_token(token)
    
    assert verified_email == email


@pytest.mark.unit
def test_invalid_password_reset_token():
    """
    Test verification of invalid password reset tokens.
    
    This test verifies that invalid reset tokens
    return None when verified.
    """
    # Test with invalid token
    invalid_token = "invalid.reset.token"
    email = verify_password_reset_token(invalid_token)
    
    assert email is None
    
    # Test with empty token
    email = verify_password_reset_token("")
    assert email is None


@pytest.mark.unit
def test_password_hash_consistency():
    """
    Test that password hashing is consistent but unique.
    
    This test verifies that the same password produces
    different hashes (due to salt) but both verify correctly.
    """
    password = "TestPassword123"
    
    # Hash the same password twice
    hash1 = get_password_hash(password)
    hash2 = get_password_hash(password)
    
    # Hashes should be different (due to salt)
    assert hash1 != hash2
    
    # Both should verify correctly
    assert verify_password(password, hash1) is True
    assert verify_password(password, hash2) is True
