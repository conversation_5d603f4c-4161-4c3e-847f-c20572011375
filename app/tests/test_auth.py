"""
Tests for authentication functionality.

This module contains comprehensive tests for user authentication,
registration, and authorization features.
"""

import pytest
from fastapi.testclient import <PERSON><PERSON><PERSON>
from httpx import Async<PERSON><PERSON>
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.security import create_access_token, get_password_hash
from app.modules.user_management.models import User
from app.modules.user_management.services import UserService


@pytest.mark.unit
def test_password_hashing():
    """Test password hashing functionality."""
    password = "TestPassword123"
    hashed = get_password_hash(password)
    
    assert hashed != password
    assert len(hashed) > 0


@pytest.mark.unit
def test_access_token_creation():
    """Test JWT access token creation."""
    user_id = "test-user-123"
    token = create_access_token(subject=user_id)
    
    assert isinstance(token, str)
    assert len(token) > 0


@pytest.mark.integration
@pytest.mark.asyncio
async def test_user_registration(async_client: AsyncClient):
    """Test user registration endpoint."""
    user_data = {
        "email": "<EMAIL>",
        "username": "testuser",
        "password": "TestPassword123",
        "full_name": "Test User"
    }
    
    response = await async_client.post("/api/v1/users/register", json=user_data)
    
    # Registration might be disabled by default
    assert response.status_code in [201, 403]
    
    if response.status_code == 201:
        data = response.json()
        assert "access_token" in data
        assert "user" in data
        assert data["user"]["email"] == user_data["email"]
        assert data["user"]["username"] == user_data["username"]


@pytest.mark.integration
@pytest.mark.asyncio
async def test_user_login_invalid_credentials(async_client: AsyncClient):
    """Test login with invalid credentials."""
    login_data = {
        "email": "<EMAIL>",
        "password": "wrongpassword"
    }
    
    response = await async_client.post("/api/v1/auth/login", json=login_data)
    
    assert response.status_code == 401
    data = response.json()
    assert "error_code" in data


@pytest.mark.integration
@pytest.mark.asyncio
async def test_user_service_create_user(db_session: AsyncSession, sample_user_data):
    """Test user creation through service layer."""
    from app.modules.user_management.schemas import UserCreate
    
    user_service = UserService(db_session)
    user_create = UserCreate(**sample_user_data)
    
    user = await user_service.create_user(user_create)
    
    assert user.email == sample_user_data["email"]
    assert user.username == sample_user_data["username"]
    assert user.full_name == sample_user_data["full_name"]
    assert user.is_active is True
    assert user.is_verified is False


@pytest.mark.integration
@pytest.mark.asyncio
async def test_user_service_duplicate_email(db_session: AsyncSession, sample_user_data):
    """Test user creation with duplicate email."""
    from app.modules.user_management.schemas import UserCreate
    
    user_service = UserService(db_session)
    user_create = UserCreate(**sample_user_data)
    
    # Create first user
    await user_service.create_user(user_create)
    
    # Try to create second user with same email
    duplicate_data = sample_user_data.copy()
    duplicate_data["username"] = "differentuser"
    user_create_duplicate = UserCreate(**duplicate_data)
    
    with pytest.raises(ValueError, match="Email already registered"):
        await user_service.create_user(user_create_duplicate)


@pytest.mark.integration
@pytest.mark.asyncio
async def test_user_service_duplicate_username(db_session: AsyncSession, sample_user_data):
    """Test user creation with duplicate username."""
    from app.modules.user_management.schemas import UserCreate
    
    user_service = UserService(db_session)
    user_create = UserCreate(**sample_user_data)
    
    # Create first user
    await user_service.create_user(user_create)
    
    # Try to create second user with same username
    duplicate_data = sample_user_data.copy()
    duplicate_data["email"] = "<EMAIL>"
    user_create_duplicate = UserCreate(**duplicate_data)
    
    with pytest.raises(ValueError, match="Username already taken"):
        await user_service.create_user(user_create_duplicate)


@pytest.mark.integration
@pytest.mark.asyncio
async def test_user_authentication(db_session: AsyncSession, sample_user_data):
    """Test user authentication through service layer."""
    from app.modules.user_management.schemas import UserCreate, UserLogin
    
    user_service = UserService(db_session)
    user_create = UserCreate(**sample_user_data)
    
    # Create user
    created_user = await user_service.create_user(user_create)
    
    # Test successful authentication
    login_data = UserLogin(
        email=sample_user_data["email"],
        password=sample_user_data["password"]
    )
    authenticated_user = await user_service.authenticate_user(login_data)
    
    assert authenticated_user is not None
    assert authenticated_user.id == created_user.id
    assert authenticated_user.email == sample_user_data["email"]


@pytest.mark.integration
@pytest.mark.asyncio
async def test_user_authentication_wrong_password(db_session: AsyncSession, sample_user_data):
    """Test user authentication with wrong password."""
    from app.modules.user_management.schemas import UserCreate, UserLogin
    
    user_service = UserService(db_session)
    user_create = UserCreate(**sample_user_data)
    
    # Create user
    await user_service.create_user(user_create)
    
    # Test authentication with wrong password
    login_data = UserLogin(
        email=sample_user_data["email"],
        password="wrongpassword"
    )
    authenticated_user = await user_service.authenticate_user(login_data)
    
    assert authenticated_user is None


@pytest.mark.integration
@pytest.mark.asyncio
async def test_user_profile_update(db_session: AsyncSession, sample_user_data):
    """Test user profile update through service layer."""
    from app.modules.user_management.schemas import UserCreate, UserUpdate
    
    user_service = UserService(db_session)
    user_create = UserCreate(**sample_user_data)
    
    # Create user
    created_user = await user_service.create_user(user_create)
    
    # Update user profile
    update_data = UserUpdate(
        full_name="Updated Name",
        bio="Updated bio"
    )
    updated_user = await user_service.update_user(created_user.id, update_data)
    
    assert updated_user is not None
    assert updated_user.full_name == "Updated Name"
    assert updated_user.bio == "Updated bio"
    assert updated_user.email == sample_user_data["email"]  # Unchanged


@pytest.mark.integration
@pytest.mark.asyncio
async def test_get_user_by_id(db_session: AsyncSession, sample_user_data):
    """Test getting user by ID."""
    from app.modules.user_management.schemas import UserCreate
    
    user_service = UserService(db_session)
    user_create = UserCreate(**sample_user_data)
    
    # Create user
    created_user = await user_service.create_user(user_create)
    
    # Get user by ID
    retrieved_user = await user_service.get_user_by_id(created_user.id)
    
    assert retrieved_user is not None
    assert retrieved_user.id == created_user.id
    assert retrieved_user.email == sample_user_data["email"]


@pytest.mark.integration
@pytest.mark.asyncio
async def test_get_user_by_email(db_session: AsyncSession, sample_user_data):
    """Test getting user by email."""
    from app.modules.user_management.schemas import UserCreate
    
    user_service = UserService(db_session)
    user_create = UserCreate(**sample_user_data)
    
    # Create user
    created_user = await user_service.create_user(user_create)
    
    # Get user by email
    retrieved_user = await user_service.get_user_by_email(sample_user_data["email"])
    
    assert retrieved_user is not None
    assert retrieved_user.id == created_user.id
    assert retrieved_user.email == sample_user_data["email"]
