# Environment Configuration for Supplement Tracker

# Application Settings
SUPPLEMENT_TRACKER_DEBUG=false
SUPPLEMENT_TRACKER_TESTING=false
SUPPLEMENT_TRACKER_PROJECT_NAME="Supplement Tracker"
SUPPLEMENT_TRACKER_API_V1_STR="/api/v1"

# Server Configuration
SUPPLEMENT_TRACKER_SERVER_NAME="localhost"
SUPPLEMENT_TRACKER_SERVER_HOST="http://localhost"
SUPPLEMENT_TRACKER_BACKEND_CORS_ORIGINS="http://localhost:3000,http://localhost:8080"

# Security
SUPPLEMENT_TRACKER_SECRET_KEY="your-secret-key-here-change-in-production"
SUPPLEMENT_TRACKER_ACCESS_TOKEN_EXPIRE_MINUTES=11520  # 8 days
SUPPLEMENT_TRACKER_EMAIL_RESET_TOKEN_EXPIRE_HOURS=48
SUPPLEMENT_TRACKER_BCRYPT_ROUNDS=12

# Database Configuration
SUPPLEMENT_TRACKER_POSTGRES_SERVER="localhost"
SUPPLEMENT_TRACKER_POSTGRES_USER="supplement_user"
SUPPLEMENT_TRACKER_POSTGRES_PASSWORD="supplement_password"
SUPPLEMENT_TRACKER_POSTGRES_DB="supplement_tracker"
# Optional: Override with full connection string
# SUPPLEMENT_TRACKER_SQLALCHEMY_DATABASE_URI="postgresql+asyncpg://user:pass@localhost/dbname"

# Redis Configuration
SUPPLEMENT_TRACKER_REDIS_URL="redis://localhost:6379/0"

# Elasticsearch Configuration
SUPPLEMENT_TRACKER_ELASTICSEARCH_URL="http://localhost:9200"

# Celery Configuration
SUPPLEMENT_TRACKER_CELERY_BROKER_URL="redis://localhost:6379/1"
SUPPLEMENT_TRACKER_CELERY_RESULT_BACKEND="redis://localhost:6379/2"

# User Management
SUPPLEMENT_TRACKER_FIRST_SUPERUSER="<EMAIL>"
SUPPLEMENT_TRACKER_FIRST_SUPERUSER_PASSWORD="changeme"
SUPPLEMENT_TRACKER_USERS_OPEN_REGISTRATION=false
SUPPLEMENT_TRACKER_EMAIL_TEST_USER="<EMAIL>"

# File Upload Settings
SUPPLEMENT_TRACKER_MAX_UPLOAD_SIZE=10485760  # 10MB in bytes
SUPPLEMENT_TRACKER_ALLOWED_EXTENSIONS=".jpg,.jpeg,.png,.pdf,.csv"

# Rate Limiting
SUPPLEMENT_TRACKER_RATE_LIMIT_PER_MINUTE=60

# Pagination
SUPPLEMENT_TRACKER_DEFAULT_PAGE_SIZE=20
SUPPLEMENT_TRACKER_MAX_PAGE_SIZE=100

# Monitoring and Error Tracking
# SUPPLEMENT_TRACKER_SENTRY_DSN="https://your-sentry-dsn-here"

# AWS Configuration (for file storage)
# AWS_ACCESS_KEY_ID="your-aws-access-key"
# AWS_SECRET_ACCESS_KEY="your-aws-secret-key"
# AWS_DEFAULT_REGION="us-east-1"
# AWS_S3_BUCKET="supplement-tracker-files"

# Email Configuration (for notifications)
# SMTP_TLS=true
# SMTP_PORT=587
# SMTP_HOST="smtp.gmail.com"
# SMTP_USER="<EMAIL>"
# SMTP_PASSWORD="your-app-password"
