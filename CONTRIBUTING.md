# Contributing to Supplement Tracker

Thank you for your interest in contributing to the Community-Driven Supplement Research Platform! This document provides guidelines and information for contributors.

## 🤝 Code of Conduct

We are committed to providing a welcoming and inclusive environment for all contributors. Please read and follow our Code of Conduct.

## 🚀 Getting Started

### Development Setup

1. **Fork and Clone**
   ```bash
   git clone https://github.com/YOUR_USERNAME/day2-supplement-tracker.git
   cd day2-supplement-tracker
   ```

2. **Set up Development Environment**

   **Option A: Using Nix (Recommended)**
   ```bash
   nix-shell  # Automatically sets up Python and all dependencies
   # All dependencies are now available - no additional installation needed!
   ```

   **Option B: Traditional Setup**
   ```bash
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   pip install -r requirements-dev.txt
   ```

3. **Install Pre-commit Hooks**
   ```bash
   pre-commit install
   ```

4. **Set up Environment**
   ```bash
   cp .env.example .env
   # Edit .env with your local configuration
   ```

## 📋 Development Guidelines

### Code Quality Standards

We maintain high code quality standards:

- **PEP 8**: Python style guide compliance
- **PEP 257**: Comprehensive docstrings for all functions and classes
- **PEP 484**: Complete type hints throughout the codebase
- **90% test coverage** minimum requirement
- **Security**: Follow OWASP guidelines for web application security

### Code Style

- Use **Black** for code formatting (line length: 88)
- Use **isort** for import sorting
- Follow **Google-style docstrings**
- Use **meaningful variable and function names**
- Keep functions small and focused (max 50 lines)

### Type Hints

All code must include comprehensive type hints:

```python
from typing import List, Optional, Dict, Any
from pydantic import BaseModel

async def process_supplements(
    user_id: str,
    supplements: List[Dict[str, Any]],
    validate: bool = True
) -> Optional[Dict[str, Any]]:
    """
    Process supplement data for a user.
    
    Args:
        user_id: UUID of the user
        supplements: List of supplement data dictionaries
        validate: Whether to validate supplement data
        
    Returns:
        Processing result dictionary or None if failed
        
    Raises:
        ValidationError: If supplement data is invalid
    """
    # Implementation here
    pass
```

### Documentation

- **Docstrings**: All functions, classes, and modules must have docstrings
- **Type hints**: All parameters and return values must be type-hinted
- **Examples**: Include usage examples in docstrings when helpful
- **API docs**: FastAPI automatically generates API documentation

## 🧪 Testing

### Test Requirements

- **Unit tests**: Test individual functions and classes
- **Integration tests**: Test module interactions
- **API tests**: Test all endpoints
- **90% coverage**: Minimum test coverage requirement

### Writing Tests

```python
import pytest
from fastapi.testclient import TestClient

@pytest.mark.unit
def test_supplement_creation(db_session, sample_supplement_data):
    """
    Test supplement creation functionality.
    
    Args:
        db_session: Database session fixture
        sample_supplement_data: Sample supplement data fixture
    """
    # Test implementation
    pass

@pytest.mark.integration
@pytest.mark.asyncio
async def test_supplement_api_endpoint(async_client):
    """
    Test supplement API endpoint integration.
    
    Args:
        async_client: Async test client fixture
    """
    response = await async_client.post("/api/v1/supplements/", json={...})
    assert response.status_code == 201
```

### Running Tests

```bash
# Run all tests
pytest

# Run with coverage
pytest --cov=app --cov-report=html

# Run specific test types
pytest -m unit
pytest -m integration
pytest -m "not slow"

# Run specific test file
pytest app/tests/test_supplements.py
```

## 🏗️ Architecture Guidelines

### Module Structure

Follow the modular monolith architecture:

```
app/modules/your_module/
├── __init__.py
├── models.py          # SQLAlchemy models
├── schemas.py         # Pydantic schemas
├── services.py        # Business logic
├── dependencies.py    # FastAPI dependencies
└── exceptions.py      # Custom exceptions
```

### Database Models

```python
from sqlalchemy import Column, String, DateTime
from sqlalchemy.dialects.postgresql import UUID
from app.core.database import Base

class YourModel(Base):
    """
    Your model description.
    
    Attributes:
        id: Unique identifier
        name: Model name
        created_at: Creation timestamp
    """
    
    __tablename__ = "your_models"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid4)
    name = Column(String(255), nullable=False, index=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
```

### API Endpoints

```python
from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.ext.asyncio import AsyncSession
from app.core.database import get_db

router = APIRouter()

@router.post("/", response_model=YourSchema)
async def create_item(
    item_data: YourCreateSchema,
    db: AsyncSession = Depends(get_db)
) -> YourSchema:
    """
    Create a new item.
    
    Args:
        item_data: Item creation data
        db: Database session
        
    Returns:
        Created item data
        
    Raises:
        HTTPException: If creation fails
    """
    # Implementation
    pass
```

## 🔄 Workflow

### Branch Strategy

- **master**: Production-ready code
- **feature/your-feature**: Feature development
- **bugfix/issue-description**: Bug fixes
- **hotfix/critical-fix**: Critical production fixes

### Pull Request Process

1. **Create Feature Branch**
   ```bash
   git checkout -b feature/your-feature-name
   ```

2. **Make Changes**
   - Follow coding standards
   - Add comprehensive tests
   - Update documentation

3. **Commit Changes**
   ```bash
   git add .
   git commit -m "feat: add your feature description"
   ```

4. **Push and Create PR**
   ```bash
   git push origin feature/your-feature-name
   ```

5. **PR Requirements**
   - Clear description of changes
   - Link to related issues
   - All tests passing
   - Code review approval
   - No merge conflicts

### Commit Message Format

Use conventional commits:

```
type(scope): description

[optional body]

[optional footer]
```

Types:
- `feat`: New feature
- `fix`: Bug fix
- `docs`: Documentation changes
- `style`: Code style changes
- `refactor`: Code refactoring
- `test`: Test additions/changes
- `chore`: Maintenance tasks

## 🐛 Bug Reports

### Before Reporting

1. Check existing issues
2. Reproduce the bug
3. Test with latest version

### Bug Report Template

```markdown
**Bug Description**
Clear description of the bug

**Steps to Reproduce**
1. Step one
2. Step two
3. Step three

**Expected Behavior**
What should happen

**Actual Behavior**
What actually happens

**Environment**
- OS: [e.g., Ubuntu 20.04]
- Python: [e.g., 3.11.0]
- Version: [e.g., 0.1.0]

**Additional Context**
Any other relevant information
```

## 💡 Feature Requests

### Feature Request Template

```markdown
**Feature Description**
Clear description of the proposed feature

**Use Case**
Why is this feature needed?

**Proposed Solution**
How should this feature work?

**Alternatives Considered**
Other approaches you've considered

**Additional Context**
Any other relevant information
```

## 📚 Resources

- [FastAPI Documentation](https://fastapi.tiangolo.com/)
- [SQLAlchemy Documentation](https://docs.sqlalchemy.org/)
- [Pydantic Documentation](https://pydantic-docs.helpmanual.io/)
- [pytest Documentation](https://docs.pytest.org/)
- [Python Type Hints](https://docs.python.org/3/library/typing.html)

## 🆘 Getting Help

- **GitHub Discussions**: For questions and general discussion
- **GitHub Issues**: For bug reports and feature requests
- **Code Review**: Request review from maintainers

## 🙏 Recognition

Contributors will be recognized in:
- README.md contributors section
- Release notes
- Project documentation

Thank you for contributing to the Community-Driven Supplement Research Platform!
