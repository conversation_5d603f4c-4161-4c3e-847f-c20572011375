# Makefile for Supplement Tracker Development

.PHONY: help install install-dev test test-cov lint format type-check security clean run dev docs docker-build docker-run

# Default target
help:
	@echo "Available commands:"
	@echo "  install      Install production dependencies"
	@echo "  install-dev  Install development dependencies"
	@echo "  test         Run tests"
	@echo "  test-cov     Run tests with coverage"
	@echo "  lint         Run linting checks"
	@echo "  format       Format code with black and isort"
	@echo "  type-check   Run type checking with mypy"
	@echo "  security     Run security checks"
	@echo "  clean        Clean up temporary files"
	@echo "  run          Run the application"
	@echo "  dev          Run the application in development mode"
	@echo "  docs         Generate documentation"
	@echo "  docker-build Build Docker image"
	@echo "  docker-run   Run Docker container"

# Installation
install:
	pip install -r requirements.txt

install-dev:
	pip install -r requirements-dev.txt
	pre-commit install

# Testing
test:
	pytest -v

test-cov:
	pytest --cov=app --cov-report=html --cov-report=term-missing

test-unit:
	pytest -m unit -v

test-integration:
	pytest -m integration -v

# Code Quality
lint:
	flake8 app/
	bandit -r app/ -c pyproject.toml

format:
	black app/
	isort app/

type-check:
	mypy app/

security:
	bandit -r app/ -c pyproject.toml
	safety check

# Cleanup
clean:
	find . -type f -name "*.pyc" -delete
	find . -type d -name "__pycache__" -delete
	find . -type d -name "*.egg-info" -exec rm -rf {} +
	rm -rf .coverage htmlcov/ .pytest_cache/ dist/ build/

# Development
run:
	uvicorn app.main:app --host 0.0.0.0 --port 8000

dev:
	uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload

# Documentation
docs:
	mkdocs serve

docs-build:
	mkdocs build

# Database
db-upgrade:
	alembic upgrade head

db-downgrade:
	alembic downgrade -1

db-revision:
	alembic revision --autogenerate -m "$(message)"

# Docker
docker-build:
	docker build -t supplement-tracker .

docker-run:
	docker run -p 8000:8000 supplement-tracker

# Pre-commit
pre-commit:
	pre-commit run --all-files

# All quality checks
check-all: lint type-check security test

# Setup development environment
setup-dev: install-dev
	@echo "Development environment setup complete!"
	@echo "If using Nix: run 'nix-shell' first"
	@echo "Run 'make dev' to start the development server"

# Nix-specific commands
nix-shell:
	@echo "Entering Nix development shell..."
	@echo "All dependencies will be automatically available"
	nix-shell

nix-build:
	nix-build
