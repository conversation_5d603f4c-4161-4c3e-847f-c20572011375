#!/usr/bin/env python3
"""
Set up extra Python dependencies that are not available in nixpkgs.

This script creates a small virtual environment for packages that are
required for the application but not available in the Nix Python package set.
"""

import subprocess
import sys
from pathlib import Path


def run_command(cmd: list, cwd: Path = None) -> bool:
    """
    Run a command and return success status.

    Args:
        cmd: Command to run as list
        cwd: Working directory

    Returns:
        bool: True if command successful, False otherwise
    """
    try:
        subprocess.check_call(cmd, cwd=cwd)
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Command failed: {' '.join(cmd)}")
        print(f"   Error: {e}")
        return False


def main():
    """Set up extra dependencies in a virtual environment."""
    print("🔧 Setting up extra Python dependencies...")

    project_root = Path.cwd()
    venv_path = project_root / ".venv-extra"

    # List of packages not available in nixpkgs
    missing_packages = [
        "pydantic-settings==2.5.2",
        "python-jose[cryptography]==3.3.0",
        "python-multipart==0.0.6",
        "structlog==23.2.0"
    ]

    # Create virtual environment if it doesn't exist
    if not venv_path.exists():
        print("📦 Creating virtual environment for extra dependencies...")
        if not run_command([sys.executable, "-m", "venv", str(venv_path)]):
            print("❌ Failed to create virtual environment")
            return 1
        print("✅ Virtual environment created")
    else:
        print("📦 Using existing virtual environment")

    # Install packages in the virtual environment
    venv_python = venv_path / "bin" / "python"
    if not venv_python.exists():
        venv_python = venv_path / "Scripts" / "python.exe"  # Windows

    success_count = 0
    total_count = len(missing_packages)

    print("📥 Installing missing packages...")
    for package in missing_packages:
        print(f"   Installing {package}...")
        if run_command([str(venv_python), "-m", "pip", "install", package]):
            print(f"   ✅ {package}")
            success_count += 1
        else:
            print(f"   ❌ {package}")

    print(f"\n📊 Installation Summary:")
    print(f"   Successful: {success_count}/{total_count}")

    if success_count == total_count:
        print("🎉 All extra dependencies installed successfully!")
        print(f"💡 To use: source {venv_path}/bin/activate")
        return 0
    else:
        print("⚠️  Some dependencies failed to install")
        return 1


if __name__ == "__main__":
    sys.exit(main())
