{ pkgs ? import <nixpkgs> { config.allowUnfree = true; } }:

let
  python = pkgs.python311;
  pythonPackages = python.pkgs;
in

pkgs.mkShell {
  name = "supplement-tracker-dev";

  buildInputs = with pkgs; [
    # Python and core tools
    python
    pythonPackages.pip
    pythonPackages.setuptools
    pythonPackages.wheel
    pythonPackages.virtualenv
    
    # Development tools
    git
    curl
    jq
    
    # System dependencies for Python packages
    gcc
    pkg-config
    libffi
    openssl
    zlib

    # Database tools (optional - can be installed separately)
    # postgresql_15
    # redis

    # Search engine (optional - can be installed separately)
    # elasticsearch7

    # Optional: Docker for containerization
    # docker
    # docker-compose
  ];
  
  shellHook = ''
    echo "🚀 Supplement Tracker Development Environment"
    echo "============================================="
    echo ""
    echo "Python version: $(python --version)"
    echo "Pip version: $(pip --version)"
    echo ""
    echo "Available services (install separately if needed):"
    echo "  - PostgreSQL: nix-shell -p postgresql"
    echo "  - Redis: nix-shell -p redis"
    echo "  - Elasticsearch: NIXPKGS_ALLOW_UNFREE=1 nix-shell -p elasticsearch7"
    echo ""
    echo "Development commands:"
    echo "  make install-dev  - Install Python dependencies"
    echo "  make dev         - Start development server"
    echo "  make test        - Run tests"
    echo "  make format      - Format code"
    echo ""
    echo "To get started:"
    echo "  1. python -m venv venv && source venv/bin/activate"
    echo "  2. pip install -r requirements-dev.txt"
    echo "  3. cp .env.example .env"
    echo "  4. Edit .env with your configuration"
    echo "  5. make dev"
    echo ""
    
    # Set up Python path
    export PYTHONPATH="$PWD:$PYTHONPATH"
    
    # Set up environment variables for development
    export SUPPLEMENT_TRACKER_DEBUG=true
    export SUPPLEMENT_TRACKER_TESTING=false
    
    # Create local directories if they don't exist
    mkdir -p logs uploads temp
    
    # Install pre-commit hooks if not already installed
    if [ ! -f .git/hooks/pre-commit ]; then
      echo "Installing pre-commit hooks..."
      pip install pre-commit > /dev/null 2>&1 || true
      pre-commit install > /dev/null 2>&1 || true
    fi
    
    echo "Environment ready! 🎉"
  '';
  
  # Environment variables
  SUPPLEMENT_TRACKER_DEBUG = "true";
  SUPPLEMENT_TRACKER_TESTING = "false";
  
  # Python-specific environment variables
  PYTHONPATH = ".";
  PYTHONDONTWRITEBYTECODE = "1";
  PYTHONUNBUFFERED = "1";
  
  # Development-specific settings
  FLASK_ENV = "development";
  FASTAPI_ENV = "development";
}
