{ pkgs ? import <nixpkgs> {} }:

pkgs.mkShell {
  name = "supplement-tracker-dev";
  
  buildInputs = with pkgs; [
    # Python and core tools
    python311
    python311Packages.pip
    python311Packages.setuptools
    python311Packages.wheel
    
    # Development tools
    git
    curl
    jq
    
    # Database tools
    postgresql_15
    redis
    
    # Search engine
    elasticsearch7
    
    # System dependencies for Python packages
    gcc
    pkg-config
    libffi
    openssl
    zlib
    
    # Optional: Docker for containerization
    docker
    docker-compose
    
    # Optional: Node.js for frontend development
    nodejs_18
    yarn
  ];
  
  shellHook = ''
    echo "🚀 Supplement Tracker Development Environment"
    echo "============================================="
    echo ""
    echo "Python version: $(python --version)"
    echo "Pip version: $(pip --version)"
    echo ""
    echo "Available services:"
    echo "  - PostgreSQL: Use 'pg_ctl' commands or 'systemctl' if using NixOS"
    echo "  - Redis: Use 'redis-server' to start"
    echo "  - Elasticsearch: Use 'elasticsearch' to start"
    echo ""
    echo "Development commands:"
    echo "  make install-dev  - Install Python dependencies"
    echo "  make dev         - Start development server"
    echo "  make test        - Run tests"
    echo "  make format      - Format code"
    echo ""
    echo "To get started:"
    echo "  1. pip install -r requirements-dev.txt"
    echo "  2. cp .env.example .env"
    echo "  3. Edit .env with your configuration"
    echo "  4. make dev"
    echo ""
    
    # Set up Python path
    export PYTHONPATH="$PWD:$PYTHONPATH"
    
    # Set up environment variables for development
    export SUPPLEMENT_TRACKER_DEBUG=true
    export SUPPLEMENT_TRACKER_TESTING=false
    
    # Create local directories if they don't exist
    mkdir -p logs uploads temp
    
    # Install pre-commit hooks if not already installed
    if [ ! -f .git/hooks/pre-commit ]; then
      echo "Installing pre-commit hooks..."
      pip install pre-commit > /dev/null 2>&1 || true
      pre-commit install > /dev/null 2>&1 || true
    fi
    
    echo "Environment ready! 🎉"
  '';
  
  # Environment variables
  SUPPLEMENT_TRACKER_DEBUG = "true";
  SUPPLEMENT_TRACKER_TESTING = "false";
  
  # Python-specific environment variables
  PYTHONPATH = ".";
  PYTHONDONTWRITEBYTECODE = "1";
  PYTHONUNBUFFERED = "1";
  
  # Development-specific settings
  FLASK_ENV = "development";
  FASTAPI_ENV = "development";
}
