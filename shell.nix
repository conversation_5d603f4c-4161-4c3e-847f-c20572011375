{ pkgs ? import <nixpkgs> { config.allowUnfree = true; } }:

let
  python = pkgs.python311;
  pythonPackages = python.pkgs;

  # Define Python environment with all dependencies
  pythonEnv = python.withPackages (ps: with ps; [
    # Core FastAPI stack
    fastapi
    uvicorn
    pydantic
    # Note: pydantic-settings not available in nixpkgs, install via pip if needed

    # Database
    sqlalchemy
    alembic
    asyncpg
    psycopg2

    # Authentication & Security
    passlib

    # Caching & Background Tasks
    redis
    celery

    # HTTP Client
    httpx

    # Data Processing & ML
    pandas
    numpy
    scikit-learn

    # Development Dependencies
    pytest
    pytest-asyncio
    pytest-cov
    black
    flake8
    mypy

    # Additional tools
    pip
    setuptools
    wheel
  ]);
in

pkgs.mkShell {
  name = "supplement-tracker-dev";

  buildInputs = with pkgs; [
    # Python environment with all packages
    pythonEnv

    # Additional development tools not available in Python packages
    pre-commit
    
    # Development tools
    git
    curl
    jq
    
    # System dependencies for Python packages
    gcc
    pkg-config
    libffi
    openssl
    zlib

    # Database tools (optional - can be installed separately)
    # postgresql_15
    # redis

    # Search engine (optional - can be installed separately)
    # elasticsearch7

    # Optional: Docker for containerization
    # docker
    # docker-compose
  ];
  
  shellHook = ''
    echo "🚀 Supplement Tracker Development Environment"
    echo "============================================="
    echo ""
    echo "Python version: $(python --version)"
    echo "Pip version: $(pip --version)"
    echo ""
    echo "Available services (install separately if needed):"
    echo "  - PostgreSQL: nix-shell -p postgresql"
    echo "  - Redis: nix-shell -p redis"
    echo "  - Elasticsearch: NIXPKGS_ALLOW_UNFREE=1 nix-shell -p elasticsearch7"
    echo ""
    echo "Development commands:"
    echo "  make dev         - Start development server"
    echo "  make test        - Run tests"
    echo "  make format      - Format code"
    echo "  make lint        - Run linting checks"
    echo ""
    echo "To get started:"
    echo "  1. python -m venv .venv-extra"
    echo "  2. source .venv-extra/bin/activate"
    echo "  3. pip install pydantic-settings python-jose python-multipart structlog"
    echo "  4. cp .env.example .env"
    echo "  5. Edit .env with your configuration"
    echo "  6. make dev"
    echo ""
    echo "Most Python dependencies are available in this shell!"
    echo "A small venv is used only for packages not available in nixpkgs."
    echo ""

    # Set up Python path
    export PYTHONPATH="$PWD:$PYTHONPATH"

    # Add .venv-extra to PATH if it exists
    if [ -d "$PWD/.venv-extra" ]; then
      export PATH="$PWD/.venv-extra/bin:$PATH"
      export PYTHONPATH="$PWD/.venv-extra/lib/python3.11/site-packages:$PYTHONPATH"
      echo "📦 Extra dependencies loaded from .venv-extra"
    fi

    # Set up environment variables for development
    export SUPPLEMENT_TRACKER_DEBUG=true
    export SUPPLEMENT_TRACKER_TESTING=false

    # Create local directories if they don't exist
    mkdir -p logs uploads temp

    # Install pre-commit hooks if not already installed
    if [ ! -f .git/hooks/pre-commit ]; then
      echo "Installing pre-commit hooks..."
      pre-commit install > /dev/null 2>&1 || true
    fi
    
    echo "Environment ready! 🎉"
  '';
  
  # Environment variables
  SUPPLEMENT_TRACKER_DEBUG = "true";
  SUPPLEMENT_TRACKER_TESTING = "false";
  
  # Python-specific environment variables
  PYTHONPATH = ".";
  PYTHONDONTWRITEBYTECODE = "1";
  PYTHONUNBUFFERED = "1";
  
  # Development-specific settings
  FLASK_ENV = "development";
  FASTAPI_ENV = "development";
}
